package models

import "time"

type User struct {
	ID          string `json:"id" gorm:"column:id;primary_key"`
	Email       string `json:"email" gorm:"column:email;unique"`
	Password    string `json:"-" gorm:"column:password"`
	DisplayName *string `json:"display_name" gorm:"column:display_name"`

	CreatedAt   *time.Time `json:"created_at" gorm:"column:created_at"`
	CreatedByID *string    `json:"created_by_id" gorm:"column:created_by_id"`
	UpdatedAt   *time.Time `json:"updated_at" gorm:"column:updated_at"`
	UpdatedByID *string    `json:"updated_b_id" gorm:"column:updated_by_id"`
	DeletedAt   *time.Time `json:"deleted_at" gorm:"column:deleted_at"`
	DeletedByID *string    `json:"deleted_by_id" gorm:"column:deleted_by_id"`

	// Relations
	AccessTokens []UserToken `json:"access_tokens,omitempty" gorm:"foreignKey:UserID"`

	// Self-referencing relations
	CreatedBy *User `json:"created_by,omitempty" gorm:"foreignKey:CreatedByID"`
	UpdatedBy *User `json:"updated_by,omitempty" gorm:"foreignKey:UpdatedByID"`
	DeletedBy *User `json:"deleted_by,omitempty" gorm:"foreignKey:DeletedByID"`

	Projects []Project `json:"projects,omitempty" gorm:"foreignKey:CreatedBy"`
}

func (User) TableName() string {
	return "users"
}
